package com.example.biaozhu;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * 标注平台主应用启动类
 */
@SpringBootApplication
@EnableJpaAuditing
@EnableConfigurationProperties
@EntityScan(basePackages = {"com.example.biaozhu.entity"})
public class BiaoZhuApplication {

    public static void main(String[] args) {
        SpringApplication.run(BiaoZhuApplication.class, args);
    }
}