package com.example.biaozhu.entity;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 标注任务实体类
 */
@Entity
@Table(name = "tasks")
public class Task {

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        /**
         * 已创建
         */
        CREATED,
        
        /**
         * 已分配
         */
        ASSIGNED,
        
        /**
         * 进行中
         */
        IN_PROGRESS,
        
        /**
         * 已完成
         */
        COMPLETED,
        
        /**
         * 已审核
         */
        REVIEWED,
        
        /**
         * 待审核
         */
        UNDER_REVIEW,
        
        /**
         * 已拒绝
         */
        REJECTED,
        
        /**
         * 待处理
         */
        PENDING
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank
    @Size(max = 100)
    @Column(nullable = false)
    private String name;

    @Size(max = 500)
    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private String status; // CREATED, ASSIGNED, IN_PROGRESS, COMPLETED, REVIEWED

    @Column(name = "task_type", nullable = false)
    private String taskType; // CLASSIFICATION, ENTITY_RECOGNITION, SEGMENTATION, etc.

    @Column(name = "priority")
    private Integer priority = 0;
    
    @Column(name = "progress")
    private Double progress = 0.0;

    @Column(name = "start_date")
    private LocalDateTime startDate;

    @Column(name = "due_date")
    private LocalDateTime dueDate;
    
    @Column(name = "deadline")
    private Date deadline;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;
    
    @Column(name = "assigned_at")
    private Date assignedAt;
    
    @Column(name = "submitted_at")
    private Date submittedAt;
    
    @Column(name = "reviewed_at")
    private Date reviewedAt;
    
    @Column(name = "review_comments")
    private String reviewComments;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "dataset_id")
    private Dataset dataset;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_id")
    private User creator;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by_id")
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_to_id")
    private User assignedTo;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reviewer_id")
    private User reviewer;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id")
    private Project project;

    @Column(name = "annotation_count")
    private Integer annotationCount = 0;

    @Column(name = "completed_annotations")
    private Integer completedAnnotations = 0;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id")
    private AnnotationTemplate annotationTemplate;

    @OneToMany(mappedBy = "task", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<Annotation> annotations = new HashSet<>();

    // 构造函数
    public Task() {
    }

    public Task(String name, String description, String taskType, User creator, Dataset dataset) {
        this.name = name;
        this.description = description;
        this.taskType = taskType;
        this.creator = creator;
        this.dataset = dataset;
        this.status = "CREATED";
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
    
    public Double getProgress() {
        return progress;
    }
    
    public void setProgress(Double progress) {
        this.progress = progress;
    }

    public LocalDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDateTime startDate) {
        this.startDate = startDate;
    }

    public LocalDateTime getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDateTime dueDate) {
        this.dueDate = dueDate;
    }
    
    public Date getDeadline() {
        return deadline;
    }
    
    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }
    
    public Date getAssignedAt() {
        return assignedAt;
    }
    
    public void setAssignedAt(Date assignedAt) {
        this.assignedAt = assignedAt;
    }
    
    public Date getSubmittedAt() {
        return submittedAt;
    }
    
    public void setSubmittedAt(Date submittedAt) {
        this.submittedAt = submittedAt;
    }
    
    public Date getReviewedAt() {
        return reviewedAt;
    }
    
    public void setReviewedAt(Date reviewedAt) {
        this.reviewedAt = reviewedAt;
    }
    
    public String getReviewComments() {
        return reviewComments;
    }
    
    public void setReviewComments(String reviewComments) {
        this.reviewComments = reviewComments;
    }

    public Dataset getDataset() {
        return dataset;
    }

    public void setDataset(Dataset dataset) {
        this.dataset = dataset;
    }

    public User getCreator() {
        return creator;
    }

    public void setCreator(User creator) {
        this.creator = creator;
    }
    
    public User getCreatedBy() {
        return createdBy;
    }
    
    public void setCreatedBy(User createdBy) {
        this.createdBy = createdBy;
    }

    public User getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(User assignedTo) {
        this.assignedTo = assignedTo;
    }

    public User getReviewer() {
        return reviewer;
    }

    public void setReviewer(User reviewer) {
        this.reviewer = reviewer;
    }
    
    public Project getProject() {
        return project;
    }
    
    public void setProject(Project project) {
        this.project = project;
    }

    public Integer getAnnotationCount() {
        return annotationCount;
    }

    public void setAnnotationCount(Integer annotationCount) {
        this.annotationCount = annotationCount;
    }

    public Integer getCompletedAnnotations() {
        return completedAnnotations;
    }

    public void setCompletedAnnotations(Integer completedAnnotations) {
        this.completedAnnotations = completedAnnotations;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public AnnotationTemplate getAnnotationTemplate() {
        return annotationTemplate;
    }

    public void setAnnotationTemplate(AnnotationTemplate annotationTemplate) {
        this.annotationTemplate = annotationTemplate;
    }

    public Set<Annotation> getAnnotations() {
        return annotations;
    }

    public void setAnnotations(Set<Annotation> annotations) {
        this.annotations = annotations;
    }

    // 添加注解
    public void addAnnotation(Annotation annotation) {
        annotations.add(annotation);
        annotation.setTask(this);
        this.annotationCount = annotations.size();
        this.updatedAt = LocalDateTime.now();
    }

    // 移除注解
    public void removeAnnotation(Annotation annotation) {
        annotations.remove(annotation);
        annotation.setTask(null);
        this.annotationCount = annotations.size();
        this.updatedAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "Task{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", status='" + status + '\'' +
                ", taskType='" + taskType + '\'' +
                ", priority=" + priority +
                ", dataset=" + (dataset != null ? dataset.getId() : null) +
                ", createdAt=" + createdAt +
                '}';
    }
} 