package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Label;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.LabelRequest;
import com.example.biaozhu.payload.response.LabelResponse;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.service.LabelService;
import com.example.biaozhu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标签控制器
 * 处理标签管理相关请求
 */
@RestController
@RequestMapping("/labels")
public class LabelController {

    private final LabelService labelService;
    private final UserService userService;
    
    @Autowired
    public LabelController(LabelService labelService, UserService userService) {
        this.labelService = labelService;
        this.userService = userService;
    }
    
    /**
     * 创建新标签
     * 
     * @param labelRequest 标签请求对象
     * @return 创建的标签信息
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or hasRole('ANNOTATOR')")
    public ResponseEntity<?> createLabel(@Valid @RequestBody LabelRequest labelRequest) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        Label label = labelService.createLabel(labelRequest, currentUser);
        return new ResponseEntity<>(new LabelResponse(label), HttpStatus.CREATED);
    }
    
    /**
     * 批量创建标签
     * 
     * @param labelRequests 标签请求对象列表
     * @return 创建结果
     */
    @PostMapping("/batch")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> createLabelsBatch(@Valid @RequestBody List<LabelRequest> labelRequests) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());
        
        List<Label> labels = labelService.createLabelsBatch(labelRequests, currentUser);
        List<LabelResponse> responses = labels.stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList());
        
        Map<String, Object> result = new HashMap<>();
        result.put("labels", responses);
        result.put("successCount", labels.size());
        result.put("totalCount", labelRequests.size());
        
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }
    
    /**
     * 获取所有标签（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页数据
     */
    @GetMapping
    public ResponseEntity<?> getAllLabels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Label> labelsPage = labelService.getAllLabels(page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("labels", labelsPage.getContent().stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", labelsPage.getNumber());
        response.put("totalItems", labelsPage.getTotalElements());
        response.put("totalPages", labelsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据ID获取标签
     * 
     * @param id 标签ID
     * @return 标签信息响应
     */
    @GetMapping("/{id}")
    public ResponseEntity<?> getLabelById(@PathVariable Long id) {
        Label label = labelService.getLabelById(id);
        return ResponseEntity.ok(new LabelResponse(label));
    }
    
    /**
     * 根据项目ID获取标签列表
     * 
     * @param projectId 项目ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页数据
     */
    @GetMapping("/project/{projectId}")
    public ResponseEntity<?> getLabelsByProject(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Label> labelsPage = labelService.getLabelsByProject(projectId, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("labels", labelsPage.getContent().stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", labelsPage.getNumber());
        response.put("totalItems", labelsPage.getTotalElements());
        response.put("totalPages", labelsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据数据集ID获取标签列表
     * 
     * @param datasetId 数据集ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页数据
     */
    @GetMapping("/dataset/{datasetId}")
    public ResponseEntity<?> getLabelsByDataset(
            @PathVariable Long datasetId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Label> labelsPage = labelService.getLabelsByDataset(datasetId, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("labels", labelsPage.getContent().stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", labelsPage.getNumber());
        response.put("totalItems", labelsPage.getTotalElements());
        response.put("totalPages", labelsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 根据分类获取标签列表
     * 
     * @param category 标签分类
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页数据
     */
    @GetMapping("/category/{category}")
    public ResponseEntity<?> getLabelsByCategory(
            @PathVariable String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Label> labelsPage = labelService.getLabelsByCategory(category, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("labels", labelsPage.getContent().stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", labelsPage.getNumber());
        response.put("totalItems", labelsPage.getTotalElements());
        response.put("totalPages", labelsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 搜索标签
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<?> searchLabels(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        Page<Label> labelsPage = labelService.searchLabels(keyword, page, size, sort);
        
        Map<String, Object> response = new HashMap<>();
        response.put("labels", labelsPage.getContent().stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList()));
        response.put("currentPage", labelsPage.getNumber());
        response.put("totalItems", labelsPage.getTotalElements());
        response.put("totalPages", labelsPage.getTotalPages());
        
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
    
    /**
     * 更新标签信息
     * 
     * @param id 标签ID
     * @param labelRequest 标签请求对象
     * @return 更新后的标签信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @labelService.hasPermission(#id, authentication.principal.id)")
    public ResponseEntity<?> updateLabel(
            @PathVariable Long id, 
            @Valid @RequestBody LabelRequest labelRequest) {
        
        Label updatedLabel = labelService.updateLabel(id, labelRequest);
        return ResponseEntity.ok(new LabelResponse(updatedLabel));
    }
    
    /**
     * 删除标签
     * 
     * @param id 标签ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @labelService.hasPermission(#id, authentication.principal.id)")
    public ResponseEntity<?> deleteLabel(@PathVariable Long id) {
        labelService.deleteLabel(id);
        return ResponseEntity.ok(new MessageResponse("标签删除成功"));
    }
    
    /**
     * 批量删除标签
     *
     * @param ids 标签ID列表
     * @return 删除结果
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> deleteLabelsBatch(@RequestBody List<Long> ids) {
        int deletedCount = labelService.deleteLabelsBatch(ids);

        Map<String, Object> result = new HashMap<>();
        result.put("deletedCount", deletedCount);
        result.put("totalCount", ids.size());
        result.put("message", String.format("成功删除 %d 个标签", deletedCount));

        return ResponseEntity.ok(result);
    }

    /**
     * 激活/停用标签
     *
     * @param id 标签ID
     * @param active 是否激活
     * @return 更新后的标签信息
     */
    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER') or @labelService.hasPermission(#id, authentication.principal.id)")
    public ResponseEntity<?> toggleLabelStatus(
            @PathVariable Long id,
            @RequestParam boolean active) {

        Label updatedLabel = labelService.toggleLabelStatus(id, active);
        return ResponseEntity.ok(new LabelResponse(updatedLabel));
    }

    /**
     * 增加标签使用次数
     *
     * @param id 标签ID
     * @return 更新后的标签信息
     */
    @PutMapping("/{id}/usage")
    public ResponseEntity<?> incrementUsageCount(@PathVariable Long id) {
        Label updatedLabel = labelService.incrementUsageCount(id);
        return ResponseEntity.ok(new LabelResponse(updatedLabel));
    }

    /**
     * 获取热门标签
     *
     * @param limit 限制数量
     * @return 热门标签列表
     */
    @GetMapping("/popular")
    public ResponseEntity<?> getPopularLabels(@RequestParam(defaultValue = "10") int limit) {
        List<Label> popularLabels = labelService.getPopularLabels(limit);
        List<LabelResponse> responses = popularLabels.stream()
                .map(LabelResponse::new)
                .collect(Collectors.toList());

        return ResponseEntity.ok(responses);
    }

    /**
     * 获取标签统计信息
     *
     * @return 统计信息
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> getLabelStatistics() {
        Map<String, Object> statistics = labelService.getLabelStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取项目的标签统计
     *
     * @param projectId 项目ID
     * @return 统计信息
     */
    @GetMapping("/statistics/project/{projectId}")
    public ResponseEntity<?> getProjectLabelStatistics(@PathVariable Long projectId) {
        Map<String, Object> statistics = labelService.getProjectLabelStatistics(projectId);
        return ResponseEntity.ok(statistics);
    }

    /**
     * 获取各分类的标签数量
     *
     * @return 分类统计
     */
    @GetMapping("/statistics/categories")
    public ResponseEntity<?> getCategoryStatistics() {
        Map<String, Long> statistics = labelService.getCategoryStatistics();
        return ResponseEntity.ok(statistics);
    }

    /**
     * 检查标签名是否存在
     *
     * @param name 标签名称
     * @param projectId 项目ID（可选）
     * @return 是否存在
     */
    @GetMapping("/exists")
    public ResponseEntity<?> checkLabelExists(
            @RequestParam String name,
            @RequestParam(required = false) Long projectId) {

        boolean exists = labelService.existsByName(name, projectId);

        Map<String, Object> result = new HashMap<>();
        result.put("exists", exists);
        result.put("name", name);
        if (projectId != null) {
            result.put("projectId", projectId);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 复制标签到其他项目
     *
     * @param id 标签ID
     * @param targetProjectId 目标项目ID
     * @return 复制的标签信息
     */
    @PostMapping("/{id}/copy")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public ResponseEntity<?> copyLabelToProject(
            @PathVariable Long id,
            @RequestParam Long targetProjectId) {

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User currentUser = userService.getUserByUsername(authentication.getName());

        Label copiedLabel = labelService.copyLabelToProject(id, targetProjectId, currentUser);
        return new ResponseEntity<>(new LabelResponse(copiedLabel), HttpStatus.CREATED);
    }

    /**
     * 根据名称获取标签
     *
     * @param name 标签名称
     * @return 标签信息响应
     */
    @GetMapping("/name/{name}")
    public ResponseEntity<?> getLabelByName(@PathVariable String name) {
        Label label = labelService.getLabelByName(name);
        return ResponseEntity.ok(new LabelResponse(label));
    }
}
