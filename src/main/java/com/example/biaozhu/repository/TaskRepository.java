package com.example.biaozhu.repository;

import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.Task.TaskStatus;
import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务仓库接口
 */
@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    
    /**
     * 根据项目查找任务
     * @param project 项目
     * @return 任务列表
     */
    List<Task> findByProject(Project project);
    
    /**
     * 根据项目查找任务（分页）
     * @param project 项目
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findByProject(Project project, Pageable pageable);
    
    /**
     * 根据数据集查找任务
     * @param dataset 数据集
     * @return 任务列表
     */
    List<Task> findByDataset(Dataset dataset);
    
    /**
     * 根据状态查找任务
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByStatus(String status);
    
    /**
     * 根据分配给的用户查找任务
     * @param assignedTo 分配给的用户
     * @return 任务列表
     */
    List<Task> findByAssignedTo(User assignedTo);
    
    /**
     * 根据分配给的用户查找任务（分页）
     * @param assignedTo 分配给的用户
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findByAssignedTo(User assignedTo, Pageable pageable);
    
    /**
     * 根据审核人查找任务
     * @param reviewer 审核人
     * @return 任务列表
     */
    List<Task> findByReviewer(User reviewer);

    /**
     * 根据审核人查找任务（分页）
     * @param reviewer 审核人
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findByReviewer(User reviewer, Pageable pageable);

    /**
     * 根据审核人ID查找任务（分页）
     * @param reviewerId 审核人ID
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    @Query("SELECT t FROM Task t WHERE t.reviewer.id = :reviewerId")
    Page<Task> findByReviewerId(@Param("reviewerId") Long reviewerId, Pageable pageable);

    /**
     * 统计审核人的任务数量
     * @param reviewerId 审核人ID
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.reviewer.id = :reviewerId")
    long countByReviewerId(@Param("reviewerId") Long reviewerId);
    
    /**
     * 根据创建者查找任务
     * @param createdBy 创建者
     * @return 任务列表
     */
    List<Task> findByCreatedBy(User createdBy);
    
    /**
     * 根据项目和状态查找任务
     * @param project 项目
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByProjectAndStatus(Project project, String status);
    
    /**
     * 根据项目和状态查找任务（分页）
     * @param project 项目
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findByProjectAndStatus(Project project, String status, Pageable pageable);
    
    /**
     * 根据用户和状态查找任务（分页）
     * @param assignedTo 分配给的用户
     * @param status 任务状态
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findByAssignedToAndStatus(User assignedTo, String status, Pageable pageable);
    
    /**
     * 根据数据集和状态查找任务
     * @param dataset 数据集
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByDatasetAndStatus(Dataset dataset, String status);
    
    /**
     * 统计项目的任务数量
     * @param project 项目
     * @return 任务数量
     */
    Long countByProject(Project project);
    
    /**
     * 统计项目中特定状态的任务数量
     * @param project 项目
     * @param status 任务状态
     * @return 任务数量
     */
    Long countByProjectAndStatus(Project project, String status);
    
    /**
     * 根据优先级排序查找任务
     * @param pageable 分页参数
     * @return 分页任务结果
     */
    Page<Task> findAllByOrderByPriorityDesc(Pageable pageable);
    
    /**
     * 查找截止日期在指定日期之前的任务
     * @param deadline 截止日期
     * @return 任务列表
     */
    List<Task> findByDeadlineBefore(LocalDateTime deadline);
    
    /**
     * 查找用户的待审核任务
     * @param reviewer 审核人
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findByReviewerAndStatus(User reviewer, TaskStatus status);
    
    /**
     * 统计各状态的任务数量
     * @return 状态和数量的对应关系
     */
    @Query("SELECT t.status, COUNT(t) FROM Task t GROUP BY t.status")
    List<Object[]> countByStatus();
    
    /**
     * 统计用户分配的任务数量
     * @param user 用户
     * @return 任务数量
     */
    @Query("SELECT COUNT(t) FROM Task t WHERE t.assignedTo = :user")
    long countByAssignedTo(User user);
} 