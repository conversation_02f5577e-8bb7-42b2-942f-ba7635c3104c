package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Annotation;
import com.example.biaozhu.entity.DataItem;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.exception.UnauthorizedException;
import com.example.biaozhu.payload.request.AnnotationRequest;
import com.example.biaozhu.repository.AnnotationRepository;
import com.example.biaozhu.repository.DataItemRepository;
import com.example.biaozhu.repository.TaskRepository;
import com.example.biaozhu.service.AnnotationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标注服务实现类
 */
@Service
public class AnnotationServiceImpl implements AnnotationService {

    private final AnnotationRepository annotationRepository;
    private final DataItemRepository dataItemRepository;
    private final TaskRepository taskRepository;
    
    @Autowired
    public AnnotationServiceImpl(
            AnnotationRepository annotationRepository,
            DataItemRepository dataItemRepository,
            TaskRepository taskRepository) {
        this.annotationRepository = annotationRepository;
        this.dataItemRepository = dataItemRepository;
        this.taskRepository = taskRepository;
    }

    @Override
    @Transactional
    public Annotation createAnnotation(AnnotationRequest annotationRequest, User user) {
        // 验证数据项是否存在
        DataItem dataItem = dataItemRepository.findById(annotationRequest.getDataItemId())
                .orElseThrow(() -> new ResourceNotFoundException("数据项未找到"));

        // 验证任务是否存在
        Task task = null;
        if (annotationRequest.getTaskId() != null) {
            task = taskRepository.findById(annotationRequest.getTaskId())
                    .orElseThrow(() -> new ResourceNotFoundException("任务未找到"));
        }

        // 创建标注实体
        Annotation annotation = new Annotation();
        annotation.setDataItem(dataItem);
        annotation.setTask(task);
        annotation.setCreator(user);
        annotation.setContent(annotationRequest.getContent());
        annotation.setType(annotationRequest.getType());
        annotation.setStatus("SUBMITTED"); // 新增标注时设置为已提交状态
        annotation.setConfidence(annotationRequest.getConfidence());
        annotation.setCreatedAt(LocalDateTime.now());
        annotation.setUpdatedAt(LocalDateTime.now());

        // 保存标注
        Annotation savedAnnotation = annotationRepository.save(annotation);

        // 更新数据项的annotated字段为true
        if (!dataItem.isAnnotated()) {
            dataItem.setAnnotated(true);
            dataItem.setUpdatedAt(LocalDateTime.now());
            dataItemRepository.save(dataItem);
        }

        // 更新任务的completed_annotations字段
        if (task != null) {
            task.setCompletedAnnotations(task.getCompletedAnnotations() + 1);
            task.setUpdatedAt(LocalDateTime.now());
            taskRepository.save(task);
        }

        return savedAnnotation;
    }

    @Override
    @Transactional
    public List<Annotation> createAnnotationsBatch(List<AnnotationRequest> annotationRequests, User user) {
        List<Annotation> annotations = new ArrayList<>();
        
        for (AnnotationRequest request : annotationRequests) {
            try {
                annotations.add(createAnnotation(request, user));
            } catch (Exception e) {
                // 记录错误但继续处理
                System.err.println("批量创建标注时出错: " + e.getMessage());
            }
        }
        
        return annotations;
    }

    @Override
    public Page<Annotation> getAllAnnotations(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return annotationRepository.findAll(pageable);
    }

    @Override
    public Page<Annotation> getAnnotationsByUser(Long userId, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return annotationRepository.findByCreatorId(userId, pageable);
    }

    @Override
    public Page<Annotation> getAnnotationsByTask(Long taskId, int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return annotationRepository.findByTaskId(taskId, pageable);
    }

    @Override
    public List<Annotation> getAnnotationsByDataItem(Long dataItemId) {
        return annotationRepository.findByDataItemId(dataItemId);
    }

    @Override
    public Annotation getAnnotationById(Long id) {
        return annotationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("标注未找到"));
    }

    @Override
    @Transactional
    public Annotation updateAnnotation(Long id, AnnotationRequest annotationRequest) {
        Annotation annotation = getAnnotationById(id);
        
        // 只有标注创建者或管理员才能更新标注
        if (!isAnnotationCreator(id) && !hasAdminRole()) {
            throw new UnauthorizedException("无权更新此标注");
        }
        
        // 如果标注已经被审核通过，则不允许再修改
        if ("APPROVED".equals(annotation.getStatus())) {
            throw new UnauthorizedException("已通过审核的标注不允许修改");
        }
        
        // 更新标注内容
        annotation.setContent(annotationRequest.getContent());
        annotation.setType(annotationRequest.getType());
        annotation.setConfidence(annotationRequest.getConfidence());
        annotation.setStatus("PENDING"); // 更新后重置为待审核状态
        annotation.setUpdatedAt(LocalDateTime.now());
        
        return annotationRepository.save(annotation);
    }

    @Override
    @Transactional
    public void deleteAnnotation(Long id) {
        Annotation annotation = getAnnotationById(id);
        
        // 只有标注创建者或管理员才能删除标注
        if (!isAnnotationCreator(id) && !hasAdminRole()) {
            throw new UnauthorizedException("无权删除此标注");
        }
        
        annotationRepository.delete(annotation);
    }

    @Override
    @Transactional
    public int deleteAnnotationsBatch(List<Long> ids) {
        int count = 0;
        for (Long id : ids) {
            try {
                deleteAnnotation(id);
                count++;
            } catch (Exception e) {
                // 记录错误但继续处理
                System.err.println("批量删除标注时出错: " + e.getMessage());
            }
        }
        return count;
    }

    @Override
    @Transactional
    public void reviewAnnotation(Long id, User reviewer, boolean approved, String comments) {
        Annotation annotation = getAnnotationById(id);
        
        // 不能审核自己的标注
        if (annotation.getCreator().getId().equals(reviewer.getId())) {
            throw new UnauthorizedException("不能审核自己的标注");
        }
        
        annotation.setStatus(approved ? "APPROVED" : "REJECTED");
        annotation.setReviewer(reviewer);
        annotation.setReviewedAt(LocalDateTime.now());
        annotation.setReviewComments(comments);
        annotation.setUpdatedAt(LocalDateTime.now());
        
        annotationRepository.save(annotation);
    }

    @Override
    @Transactional
    public int reviewAnnotationsBatch(List<Long> ids, User reviewer, boolean approved, String comments) {
        int count = 0;
        for (Long id : ids) {
            try {
                reviewAnnotation(id, reviewer, approved, comments);
                count++;
            } catch (Exception e) {
                // 记录错误但继续处理
                System.err.println("批量审核标注时出错: " + e.getMessage());
            }
        }
        return count;
    }

    @Override
    public ResponseEntity<?> exportAnnotations(Long taskId, String format) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("任务未找到"));
                
        List<Annotation> annotations = annotationRepository.findByTaskId(taskId);
        
        String fileName = "task_" + taskId + "_annotations";
        
        if ("csv".equalsIgnoreCase(format)) {
            return exportToCsv(annotations, fileName);
        } else {
            return exportToJson(annotations, fileName);
        }
    }

    private ResponseEntity<?> exportToJson(List<Annotation> annotations, String fileName) {
        // 这里简化了JSON序列化过程，实际实现需要更完善的处理
        StringBuilder json = new StringBuilder("[\n");
        for (int i = 0; i < annotations.size(); i++) {
            Annotation annotation = annotations.get(i);
            json.append("  {\n");
            json.append("    \"id\": ").append(annotation.getId()).append(",\n");
            json.append("    \"dataItemId\": ").append(annotation.getDataItem().getId()).append(",\n");
            json.append("    \"content\": ").append("\"").append(escapeJson(annotation.getContent())).append("\"").append(",\n");
            json.append("    \"type\": ").append("\"").append(annotation.getType()).append("\"").append(",\n");
            json.append("    \"status\": ").append("\"").append(annotation.getStatus()).append("\"").append(",\n");
            json.append("    \"confidence\": ").append(annotation.getConfidence()).append(",\n");
            json.append("    \"creator\": ").append("\"").append(annotation.getCreator().getUsername()).append("\"").append(",\n");
            json.append("    \"createdAt\": ").append("\"").append(annotation.getCreatedAt()).append("\"").append("\n");
            json.append("  }");
            if (i < annotations.size() - 1) {
                json.append(",");
            }
            json.append("\n");
        }
        json.append("]");
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setContentDispositionFormData("attachment", fileName + ".json");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(json.toString());
    }

    private ResponseEntity<?> exportToCsv(List<Annotation> annotations, String fileName) {
        StringBuilder csv = new StringBuilder();
        csv.append("id,dataItemId,content,type,status,confidence,creator,createdAt\n");
        
        for (Annotation annotation : annotations) {
            csv.append(annotation.getId()).append(",");
            csv.append(annotation.getDataItem().getId()).append(",");
            csv.append("\"").append(escapeCsv(annotation.getContent())).append("\"").append(",");
            csv.append(annotation.getType()).append(",");
            csv.append(annotation.getStatus()).append(",");
            csv.append(annotation.getConfidence()).append(",");
            csv.append(annotation.getCreator().getUsername()).append(",");
            csv.append(annotation.getCreatedAt()).append("\n");
        }
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.TEXT_PLAIN);
        headers.setContentDispositionFormData("attachment", fileName + ".csv");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(csv.toString());
    }

    private String escapeJson(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
    }

    private String escapeCsv(String input) {
        if (input == null) {
            return "";
        }
        return input.replace("\"", "\"\"");
    }

    @Override
    public Map<String, Object> getAnnotationStatistics(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("任务未找到"));
                
        List<Annotation> annotations = annotationRepository.findByTaskId(taskId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalAnnotations", annotations.size());
        
        // 按状态统计
        Map<String, Long> statusCounts = annotations.stream()
                .collect(Collectors.groupingBy(Annotation::getStatus, Collectors.counting()));
        statistics.put("statusCounts", statusCounts);
        
        // 按类型统计
        Map<String, Long> typeCounts = annotations.stream()
                .collect(Collectors.groupingBy(Annotation::getType, Collectors.counting()));
        statistics.put("typeCounts", typeCounts);
        
        // 按标注者统计
        Map<String, Long> creatorCounts = annotations.stream()
                .collect(Collectors.groupingBy(a -> a.getCreator().getUsername(), Collectors.counting()));
        statistics.put("creatorCounts", creatorCounts);
        
        // 计算平均置信度
        double avgConfidence = annotations.stream()
                .mapToDouble(Annotation::getConfidence)
                .average()
                .orElse(0.0);
        statistics.put("averageConfidence", avgConfidence);
        
        return statistics;
    }

    @Override
    public Map<String, Object> getUserAnnotationPerformance(Long userId, int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        
        // 检查Annotation实体中是否使用creator而不是createdBy
        List<Annotation> userAnnotations = annotationRepository.findByCreatorIdAndCreatedAtAfter(userId, startDate);
        
        Map<String, Object> performance = new HashMap<>();
        performance.put("userId", userId);
        performance.put("periodDays", days);
        performance.put("totalAnnotations", userAnnotations.size());
        
        // 按状态统计
        Map<String, Long> statusCounts = userAnnotations.stream()
                .collect(Collectors.groupingBy(Annotation::getStatus, Collectors.counting()));
        performance.put("statusCounts", statusCounts);
        
        // 计算平均置信度
        double avgConfidence = userAnnotations.stream()
                .mapToDouble(Annotation::getConfidence)
                .average()
                .orElse(0.0);
        performance.put("averageConfidence", avgConfidence);
        
        // 计算每日标注数量
        Map<String, Long> dailyCounts = userAnnotations.stream()
                .collect(Collectors.groupingBy(
                        a -> a.getCreatedAt().toLocalDate().toString(),
                        Collectors.counting())
                );
        performance.put("dailyCounts", dailyCounts);
        
        // 计算审核通过率
        long approvedCount = userAnnotations.stream()
                .filter(a -> "APPROVED".equals(a.getStatus()))
                .count();
        long reviewedCount = userAnnotations.stream()
                .filter(a -> "APPROVED".equals(a.getStatus()) || "REJECTED".equals(a.getStatus()))
                .count();
        
        double approvalRate = reviewedCount > 0 ? (double) approvedCount / reviewedCount : 0.0;
        performance.put("approvalRate", approvalRate);
        
        return performance;
    }

    @Override
    public boolean isAnnotationCreator(Long annotationId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String username = authentication.getName();
        Annotation annotation = annotationRepository.findById(annotationId).orElse(null);

        return annotation != null && annotation.getCreator().getUsername().equals(username);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<Annotation> getUnreviewedAnnotationsByTask(Long taskId, int page, int size, String sort) {
        // 验证任务是否存在
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new ResourceNotFoundException("任务未找到"));

        Pageable pageable = PageRequest.of(page, size, Sort.by(sort));
        return annotationRepository.findUnreviewedByTaskId(taskId, pageable);
    }

    /**
     * 检查当前用户是否具有ADMIN角色
     *
     * @return 是否是管理员
     */
    private boolean hasAdminRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
    }
}