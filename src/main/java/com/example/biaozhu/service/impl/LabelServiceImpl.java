package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Label;
import com.example.biaozhu.entity.Project;
import com.example.biaozhu.entity.Dataset;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.BadRequestException;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.exception.UnauthorizedException;
import com.example.biaozhu.payload.request.LabelRequest;
import com.example.biaozhu.repository.LabelRepository;
import com.example.biaozhu.repository.ProjectRepository;
import com.example.biaozhu.repository.DatasetRepository;
import com.example.biaozhu.service.LabelService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签服务实现类
 */
@Service
@Transactional
public class LabelServiceImpl implements LabelService {
    
    private static final Logger logger = LoggerFactory.getLogger(LabelServiceImpl.class);
    
    private final LabelRepository labelRepository;
    private final ProjectRepository projectRepository;
    private final DatasetRepository datasetRepository;
    
    @Autowired
    public LabelServiceImpl(LabelRepository labelRepository, 
                           ProjectRepository projectRepository,
                           DatasetRepository datasetRepository) {
        this.labelRepository = labelRepository;
        this.projectRepository = projectRepository;
        this.datasetRepository = datasetRepository;
    }
    
    @Override
    public Label createLabel(LabelRequest labelRequest, User creator) {
        logger.info("创建新标签: {}", labelRequest.getName());
        
        // 验证标签名称在项目内的唯一性
        if (labelRequest.getProjectId() != null) {
            Project project = projectRepository.findById(labelRequest.getProjectId())
                .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));
            
            if (labelRepository.findByProjectAndName(project, labelRequest.getName()).isPresent()) {
                throw new BadRequestException("该项目中已存在同名标签");
            }
        } else if (labelRepository.findByName(labelRequest.getName()).isPresent()) {
            throw new BadRequestException("标签名称已存在");
        }
        
        Label label = Label.builder()
            .name(labelRequest.getName())
            .description(labelRequest.getDescription())
            .color(labelRequest.getColor())
            .category(labelRequest.getCategory())
            .shortcut(labelRequest.getShortcut())
            .active(labelRequest.getActive() != null ? labelRequest.getActive() : true)
            .usageCount(0)
            .iconUrl(labelRequest.getIconUrl())
            .priority(labelRequest.getPriority() != null ? labelRequest.getPriority() : 0)
            .metadata(labelRequest.getMetadata())
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .createdBy(creator)
            .build();
        
        // 设置项目关联
        if (labelRequest.getProjectId() != null) {
            Project project = projectRepository.findById(labelRequest.getProjectId())
                .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));
            label.setProject(project);
        }
        
        // 设置数据集关联
        if (labelRequest.getDatasetId() != null) {
            Dataset dataset = datasetRepository.findById(labelRequest.getDatasetId())
                .orElseThrow(() -> new ResourceNotFoundException("数据集未找到"));
            label.setDataset(dataset);
        }
        
        Label savedLabel = labelRepository.save(label);
        logger.info("标签已创建, id: {}", savedLabel.getId());
        
        return savedLabel;
    }
    
    @Override
    public List<Label> createLabelsBatch(List<LabelRequest> labelRequests, User creator) {
        logger.info("批量创建标签, 数量: {}", labelRequests.size());
        
        List<Label> labels = new ArrayList<>();
        for (LabelRequest request : labelRequests) {
            try {
                Label label = createLabel(request, creator);
                labels.add(label);
            } catch (Exception e) {
                logger.error("批量创建标签时出错: {}", e.getMessage());
                // 继续处理其他标签
            }
        }
        
        logger.info("批量创建标签完成, 成功创建: {}", labels.size());
        return labels;
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Label> getAllLabels(int page, int size, String sort) {
        logger.info("获取所有标签, 页码: {}, 大小: {}, 排序: {}", page, size, sort);
        
        Sort sortObj = Sort.by(Sort.Direction.DESC, sort);
        Pageable pageable = PageRequest.of(page, size, sortObj);
        
        return labelRepository.findAll(pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Label getLabelById(Long id) {
        logger.info("根据ID获取标签: {}", id);
        
        return labelRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("标签未找到"));
    }
    
    @Override
    @Transactional(readOnly = true)
    public Label getLabelByName(String name) {
        logger.info("根据名称获取标签: {}", name);
        
        return labelRepository.findByName(name)
            .orElseThrow(() -> new ResourceNotFoundException("标签未找到"));
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Label> getLabelsByProject(Long projectId, int page, int size, String sort) {
        logger.info("获取项目标签, 项目ID: {}", projectId);
        
        Project project = projectRepository.findById(projectId)
            .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));
        
        Sort sortObj = Sort.by(Sort.Direction.DESC, sort);
        Pageable pageable = PageRequest.of(page, size, sortObj);
        
        return labelRepository.findByProject(project, pageable);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Label> getLabelsByDataset(Long datasetId, int page, int size, String sort) {
        logger.info("获取数据集标签, 数据集ID: {}", datasetId);
        
        Dataset dataset = datasetRepository.findById(datasetId)
            .orElseThrow(() -> new ResourceNotFoundException("数据集未找到"));
        
        Sort sortObj = Sort.by(Sort.Direction.DESC, sort);
        Pageable pageable = PageRequest.of(page, size, sortObj);
        
        // 注意：这里需要在LabelRepository中添加相应的方法
        List<Label> labels = labelRepository.findByDataset(dataset);
        
        // 手动分页处理
        int start = page * size;
        int end = Math.min(start + size, labels.size());
        List<Label> pageContent = labels.subList(start, end);
        
        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, labels.size());
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Label> getLabelsByCategory(String category, int page, int size, String sort) {
        logger.info("获取分类标签, 分类: {}", category);
        
        Sort sortObj = Sort.by(Sort.Direction.DESC, sort);
        Pageable pageable = PageRequest.of(page, size, sortObj);
        
        List<Label> labels = labelRepository.findByCategory(category);
        
        // 手动分页处理
        int start = page * size;
        int end = Math.min(start + size, labels.size());
        List<Label> pageContent = labels.subList(start, end);
        
        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, labels.size());
    }
    
    @Override
    @Transactional(readOnly = true)
    public Page<Label> searchLabels(String keyword, int page, int size, String sort) {
        logger.info("搜索标签, 关键词: {}", keyword);
        
        Sort sortObj = Sort.by(Sort.Direction.DESC, sort);
        Pageable pageable = PageRequest.of(page, size, sortObj);
        
        List<Label> labels = labelRepository.findByNameContaining(keyword);
        
        // 手动分页处理
        int start = page * size;
        int end = Math.min(start + size, labels.size());
        List<Label> pageContent = labels.subList(start, end);
        
        return new org.springframework.data.domain.PageImpl<>(pageContent, pageable, labels.size());
    }
    
    @Override
    public Label updateLabel(Long id, LabelRequest labelRequest) {
        logger.info("更新标签: {}", id);
        
        Label label = getLabelById(id);
        
        // 检查权限
        if (!hasPermission(id, getCurrentUserId())) {
            throw new UnauthorizedException("无权限修改此标签");
        }
        
        // 检查名称唯一性（如果名称发生变化）
        if (!label.getName().equals(labelRequest.getName())) {
            if (labelRequest.getProjectId() != null) {
                Project project = projectRepository.findById(labelRequest.getProjectId())
                    .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));
                
                if (labelRepository.findByProjectAndName(project, labelRequest.getName()).isPresent()) {
                    throw new BadRequestException("该项目中已存在同名标签");
                }
            } else if (labelRepository.findByName(labelRequest.getName()).isPresent()) {
                throw new BadRequestException("标签名称已存在");
            }
        }
        
        // 更新标签信息
        label.setName(labelRequest.getName());
        label.setDescription(labelRequest.getDescription());
        label.setColor(labelRequest.getColor());
        label.setCategory(labelRequest.getCategory());
        label.setShortcut(labelRequest.getShortcut());
        if (labelRequest.getActive() != null) {
            label.setActive(labelRequest.getActive());
        }
        label.setIconUrl(labelRequest.getIconUrl());
        if (labelRequest.getPriority() != null) {
            label.setPriority(labelRequest.getPriority());
        }
        label.setMetadata(labelRequest.getMetadata());
        label.setUpdatedAt(LocalDateTime.now());
        
        Label savedLabel = labelRepository.save(label);
        logger.info("标签已更新: {}", savedLabel.getId());
        
        return savedLabel;
    }
    
    @Override
    public void deleteLabel(Long id) {
        logger.info("删除标签: {}", id);
        
        Label label = getLabelById(id);
        
        // 检查权限
        if (!hasPermission(id, getCurrentUserId())) {
            throw new UnauthorizedException("无权限删除此标签");
        }
        
        labelRepository.delete(label);
        logger.info("标签已删除: {}", id);
    }
    
    @Override
    public int deleteLabelsBatch(List<Long> ids) {
        logger.info("批量删除标签, 数量: {}", ids.size());
        
        int count = 0;
        for (Long id : ids) {
            try {
                deleteLabel(id);
                count++;
            } catch (Exception e) {
                logger.error("批量删除标签时出错: {}", e.getMessage());
            }
        }
        
        logger.info("批量删除标签完成, 成功删除: {}", count);
        return count;
    }
    
    @Override
    public Label toggleLabelStatus(Long id, boolean active) {
        logger.info("切换标签状态: {}, 激活: {}", id, active);

        Label label = getLabelById(id);

        // 检查权限
        if (!hasPermission(id, getCurrentUserId())) {
            throw new UnauthorizedException("无权限修改此标签");
        }

        label.setActive(active);
        label.setUpdatedAt(LocalDateTime.now());

        Label savedLabel = labelRepository.save(label);
        logger.info("标签状态已更新: {}", savedLabel.getId());

        return savedLabel;
    }

    @Override
    public Label incrementUsageCount(Long id) {
        logger.info("增加标签使用次数: {}", id);

        Label label = getLabelById(id);
        label.setUsageCount(label.getUsageCount() + 1);
        label.setUpdatedAt(LocalDateTime.now());

        return labelRepository.save(label);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Label> getPopularLabels(int limit) {
        logger.info("获取热门标签, 限制数量: {}", limit);

        List<Label> allLabels = labelRepository.findAllOrderByUsageCountDesc();
        return allLabels.stream()
            .limit(limit)
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getLabelStatistics() {
        logger.info("获取标签统计信息");

        Map<String, Object> statistics = new HashMap<>();

        // 总标签数
        long totalLabels = labelRepository.count();
        statistics.put("totalLabels", totalLabels);

        // 激活标签数
        long activeLabels = labelRepository.findByActive(true).size();
        statistics.put("activeLabels", activeLabels);

        // 各分类统计
        List<Object[]> categoryStats = labelRepository.countByCategory();
        Map<String, Long> categoryMap = new HashMap<>();
        for (Object[] stat : categoryStats) {
            String category = (String) stat[0];
            Long count = (Long) stat[1];
            categoryMap.put(category != null ? category : "未分类", count);
        }
        statistics.put("categoryStatistics", categoryMap);

        // 热门标签
        List<Label> popularLabels = getPopularLabels(10);
        statistics.put("popularLabels", popularLabels);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Object> getProjectLabelStatistics(Long projectId) {
        logger.info("获取项目标签统计: {}", projectId);

        Project project = projectRepository.findById(projectId)
            .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));

        Map<String, Object> statistics = new HashMap<>();

        // 项目标签总数
        long totalLabels = labelRepository.countByProject(project);
        statistics.put("totalLabels", totalLabels);

        // 激活标签数
        long activeLabels = labelRepository.findByProjectAndActive(project, true).size();
        statistics.put("activeLabels", activeLabels);

        // 项目标签列表
        List<Label> projectLabels = labelRepository.findByProject(project);
        statistics.put("labels", projectLabels);

        return statistics;
    }

    @Override
    @Transactional(readOnly = true)
    public Map<String, Long> getCategoryStatistics() {
        logger.info("获取分类统计");

        List<Object[]> categoryStats = labelRepository.countByCategory();
        Map<String, Long> categoryMap = new HashMap<>();

        for (Object[] stat : categoryStats) {
            String category = (String) stat[0];
            Long count = (Long) stat[1];
            categoryMap.put(category != null ? category : "未分类", count);
        }

        return categoryMap;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name, Long projectId) {
        if (projectId != null) {
            Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ResourceNotFoundException("项目未找到"));
            return labelRepository.findByProjectAndName(project, name).isPresent();
        } else {
            return labelRepository.findByName(name).isPresent();
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasPermission(Long labelId, Long userId) {
        // 简化的权限检查，实际应该根据业务需求实现
        // 这里假设标签创建者和管理员有权限
        Label label = getLabelById(labelId);

        if (label.getCreatedBy() != null && label.getCreatedBy().getId().equals(userId)) {
            return true;
        }

        // 检查是否为管理员（这里需要根据实际的权限系统实现）
        return hasAdminRole();
    }

    @Override
    public Label copyLabelToProject(Long labelId, Long targetProjectId, User creator) {
        logger.info("复制标签到项目: {} -> {}", labelId, targetProjectId);

        Label originalLabel = getLabelById(labelId);
        Project targetProject = projectRepository.findById(targetProjectId)
            .orElseThrow(() -> new ResourceNotFoundException("目标项目未找到"));

        // 检查目标项目中是否已存在同名标签
        if (labelRepository.findByProjectAndName(targetProject, originalLabel.getName()).isPresent()) {
            throw new BadRequestException("目标项目中已存在同名标签");
        }

        Label copiedLabel = Label.builder()
            .name(originalLabel.getName())
            .description(originalLabel.getDescription())
            .color(originalLabel.getColor())
            .category(originalLabel.getCategory())
            .shortcut(originalLabel.getShortcut())
            .active(originalLabel.isActive())
            .usageCount(0) // 重置使用次数
            .iconUrl(originalLabel.getIconUrl())
            .priority(originalLabel.getPriority())
            .metadata(originalLabel.getMetadata())
            .project(targetProject)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .createdBy(creator)
            .build();

        Label savedLabel = labelRepository.save(copiedLabel);
        logger.info("标签已复制: {}", savedLabel.getId());

        return savedLabel;
    }

    // 获取当前用户ID的辅助方法
    private Long getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.getPrincipal() instanceof org.springframework.security.core.userdetails.UserDetails) {
            // 这里需要根据实际的UserDetails实现来获取用户ID
            return 1L; // 临时返回，实际应该从UserDetails中获取
        }
        return null;
    }

    // 检查是否为管理员的辅助方法
    private boolean hasAdminRole() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                .anyMatch(authority -> authority.getAuthority().equals("ROLE_ADMIN"));
        }
        return false;
    }
}
