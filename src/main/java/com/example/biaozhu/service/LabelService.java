package com.example.biaozhu.service;

import com.example.biaozhu.entity.Label;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.LabelRequest;
import com.example.biaozhu.payload.response.LabelResponse;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * 标签服务接口
 */
public interface LabelService {
    
    /**
     * 创建新标签
     * 
     * @param labelRequest 标签请求对象
     * @param creator 创建者
     * @return 创建的标签
     */
    Label createLabel(LabelRequest labelRequest, User creator);
    
    /**
     * 批量创建标签
     * 
     * @param labelRequests 标签请求对象列表
     * @param creator 创建者
     * @return 创建的标签列表
     */
    List<Label> createLabelsBatch(List<LabelRequest> labelRequests, User creator);
    
    /**
     * 获取所有标签（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页结果
     */
    Page<Label> getAllLabels(int page, int size, String sort);
    
    /**
     * 根据ID获取标签
     * 
     * @param id 标签ID
     * @return 标签对象
     */
    Label getLabelById(Long id);
    
    /**
     * 根据名称获取标签
     * 
     * @param name 标签名称
     * @return 标签对象
     */
    Label getLabelByName(String name);
    
    /**
     * 根据项目ID获取标签列表
     * 
     * @param projectId 项目ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页结果
     */
    Page<Label> getLabelsByProject(Long projectId, int page, int size, String sort);
    
    /**
     * 根据数据集ID获取标签列表
     * 
     * @param datasetId 数据集ID
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页结果
     */
    Page<Label> getLabelsByDataset(Long datasetId, int page, int size, String sort);
    
    /**
     * 根据分类获取标签列表
     * 
     * @param category 标签分类
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页结果
     */
    Page<Label> getLabelsByCategory(String category, int page, int size, String sort);
    
    /**
     * 搜索标签
     * 
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 标签分页结果
     */
    Page<Label> searchLabels(String keyword, int page, int size, String sort);
    
    /**
     * 更新标签
     * 
     * @param id 标签ID
     * @param labelRequest 标签请求对象
     * @return 更新后的标签
     */
    Label updateLabel(Long id, LabelRequest labelRequest);
    
    /**
     * 删除标签
     * 
     * @param id 标签ID
     */
    void deleteLabel(Long id);
    
    /**
     * 批量删除标签
     * 
     * @param ids 标签ID列表
     * @return 删除的标签数量
     */
    int deleteLabelsBatch(List<Long> ids);
    
    /**
     * 激活/停用标签
     * 
     * @param id 标签ID
     * @param active 是否激活
     * @return 更新后的标签
     */
    Label toggleLabelStatus(Long id, boolean active);
    
    /**
     * 增加标签使用次数
     * 
     * @param id 标签ID
     * @return 更新后的标签
     */
    Label incrementUsageCount(Long id);
    
    /**
     * 获取热门标签
     * 
     * @param limit 限制数量
     * @return 热门标签列表
     */
    List<Label> getPopularLabels(int limit);
    
    /**
     * 获取标签统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getLabelStatistics();
    
    /**
     * 获取项目的标签统计
     * 
     * @param projectId 项目ID
     * @return 统计信息
     */
    Map<String, Object> getProjectLabelStatistics(Long projectId);
    
    /**
     * 获取各分类的标签数量
     * 
     * @return 分类统计
     */
    Map<String, Long> getCategoryStatistics();
    
    /**
     * 检查标签名是否存在
     * 
     * @param name 标签名称
     * @param projectId 项目ID（可选）
     * @return 是否存在
     */
    boolean existsByName(String name, Long projectId);
    
    /**
     * 检查用户是否有权限操作标签
     * 
     * @param labelId 标签ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean hasPermission(Long labelId, Long userId);
    
    /**
     * 复制标签到其他项目
     * 
     * @param labelId 标签ID
     * @param targetProjectId 目标项目ID
     * @param creator 创建者
     * @return 复制的标签
     */
    Label copyLabelToProject(Long labelId, Long targetProjectId, User creator);
}
