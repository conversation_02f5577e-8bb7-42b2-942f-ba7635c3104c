package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.Dataset;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据集响应类
 */
public class DatasetResponse {

    private Long id;
    private String name;
    private String description;
    private String type;
    private boolean isPublic;
    private int itemCount;
    private Long projectId;
    private String projectName;
    private UserSummary creator;
    private List<UserSummary> authorizedUsers;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    public DatasetResponse() {
    }

    public DatasetResponse(Dataset dataset) {
        this.id = dataset.getId();
        this.name = dataset.getName();
        this.description = dataset.getDescription();
        this.type = dataset.getType();
        this.isPublic = dataset.isPublic();
        this.itemCount = dataset.getItemCount();

        // 设置项目信息
        if (dataset.getProject() != null) {
            this.projectId = dataset.getProject().getId();
            this.projectName = dataset.getProject().getName();
        }

        if (dataset.getCreator() != null) {
            this.creator = new UserSummary(
                dataset.getCreator().getId(),
                dataset.getCreator().getUsername(),
                dataset.getCreator().getFullName()
            );
        }

        if (dataset.getAuthorizedUsers() != null) {
            this.authorizedUsers = dataset.getAuthorizedUsers().stream()
                .map(user -> new UserSummary(
                    user.getId(),
                    user.getUsername(),
                    user.getFullName()
                ))
                .collect(Collectors.toList());
        }

        this.createdAt = dataset.getCreatedAt();
        this.updatedAt = dataset.getUpdatedAt();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isPublic() {
        return isPublic;
    }

    public void setPublic(boolean isPublic) {
        this.isPublic = isPublic;
    }

    public int getItemCount() {
        return itemCount;
    }

    public void setItemCount(int itemCount) {
        this.itemCount = itemCount;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public UserSummary getCreator() {
        return creator;
    }

    public void setCreator(UserSummary creator) {
        this.creator = creator;
    }

    public List<UserSummary> getAuthorizedUsers() {
        return authorizedUsers;
    }

    public void setAuthorizedUsers(List<UserSummary> authorizedUsers) {
        this.authorizedUsers = authorizedUsers;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
} 