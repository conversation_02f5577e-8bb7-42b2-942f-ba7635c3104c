package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.DataItem;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据项分页响应类
 * 用于避免JSON序列化时的循环引用问题
 */
public class DataItemPageResponse {
    
    private boolean success;
    private List<DataItemSummary> dataItems;
    private int currentPage;
    private long totalItems;
    private int totalPages;
    private String message;
    
    public DataItemPageResponse() {
    }
    
    public DataItemPageResponse(boolean success, List<DataItemSummary> dataItems, 
                               int currentPage, long totalItems, int totalPages) {
        this.success = success;
        this.dataItems = dataItems;
        this.currentPage = currentPage;
        this.totalItems = totalItems;
        this.totalPages = totalPages;
    }
    
    /**
     * 数据项摘要信息，避免循环引用
     */
    public static class DataItemSummary {
        private Long id;
        private String name;
        private String content;
        private String type;
        private String identifier;
        private String filePath;
        private Long fileSize;
        private String fileType;
        private String splitType;
        private boolean annotated;
        private String metadata;
        private int annotationCount;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
        
        // 数据集信息
        private DatasetSummary dataset;
        
        // 创建者信息
        private UserSummary creator;
        
        public DataItemSummary() {
        }
        
        /**
         * 从DataItem实体创建摘要对象
         */
        public static DataItemSummary fromEntity(DataItem item) {
            DataItemSummary summary = new DataItemSummary();
            summary.setId(item.getId());
            summary.setName(item.getName());
            summary.setContent(item.getContent());
            summary.setType(item.getType());
            summary.setIdentifier(item.getIdentifier());
            summary.setFilePath(item.getFilePath());
            summary.setFileSize(item.getFileSize());
            summary.setFileType(item.getFileType());
            summary.setSplitType(item.getSplitType());
            summary.setAnnotated(item.isAnnotated());
            summary.setMetadata(item.getMetadata());
            summary.setCreatedAt(item.getCreatedAt());
            summary.setUpdatedAt(item.getUpdatedAt());
            summary.setAnnotationCount(item.getAnnotations() != null ? item.getAnnotations().size() : 0);
            
            // 安全地设置数据集信息
            if (item.getDataset() != null) {
                summary.setDataset(DatasetSummary.fromEntity(item.getDataset()));
            }
            
            // 安全地设置创建者信息
            if (item.getCreator() != null) {
                summary.setCreator(UserSummary.fromEntity(item.getCreator()));
            }
            
            return summary;
        }
        
        /**
         * 批量转换DataItem列表为摘要列表
         */
        public static List<DataItemSummary> fromEntityList(List<DataItem> items) {
            return items.stream()
                    .map(DataItemSummary::fromEntity)
                    .collect(Collectors.toList());
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        
        public String getIdentifier() { return identifier; }
        public void setIdentifier(String identifier) { this.identifier = identifier; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        
        public String getSplitType() { return splitType; }
        public void setSplitType(String splitType) { this.splitType = splitType; }
        
        public boolean isAnnotated() { return annotated; }
        public void setAnnotated(boolean annotated) { this.annotated = annotated; }
        
        public String getMetadata() { return metadata; }
        public void setMetadata(String metadata) { this.metadata = metadata; }
        
        public int getAnnotationCount() { return annotationCount; }
        public void setAnnotationCount(int annotationCount) { this.annotationCount = annotationCount; }
        
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
        
        public LocalDateTime getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
        
        public DatasetSummary getDataset() { return dataset; }
        public void setDataset(DatasetSummary dataset) { this.dataset = dataset; }
        
        public UserSummary getCreator() { return creator; }
        public void setCreator(UserSummary creator) { this.creator = creator; }
    }
    
    /**
     * 数据集摘要信息
     */
    public static class DatasetSummary {
        private Long id;
        private String name;
        
        public DatasetSummary() {
        }
        
        public static DatasetSummary fromEntity(com.example.biaozhu.entity.Dataset dataset) {
            DatasetSummary summary = new DatasetSummary();
            summary.setId(dataset.getId());
            summary.setName(dataset.getName());
            return summary;
        }
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
    
    /**
     * 用户摘要信息
     */
    public static class UserSummary {
        private Long id;
        private String username;
        private String fullName;
        
        public UserSummary() {
        }
        
        public static UserSummary fromEntity(com.example.biaozhu.entity.User user) {
            UserSummary summary = new UserSummary();
            summary.setId(user.getId());
            summary.setUsername(user.getUsername());
            summary.setFullName(user.getFullName());
            return summary;
        }
        
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
    }
    
    // Main class getters and setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public List<DataItemSummary> getDataItems() { return dataItems; }
    public void setDataItems(List<DataItemSummary> dataItems) { this.dataItems = dataItems; }
    
    public int getCurrentPage() { return currentPage; }
    public void setCurrentPage(int currentPage) { this.currentPage = currentPage; }
    
    public long getTotalItems() { return totalItems; }
    public void setTotalItems(long totalItems) { this.totalItems = totalItems; }
    
    public int getTotalPages() { return totalPages; }
    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
}
