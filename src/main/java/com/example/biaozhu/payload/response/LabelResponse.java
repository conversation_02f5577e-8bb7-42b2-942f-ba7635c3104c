package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.Label;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 标签响应类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LabelResponse {
    
    /**
     * 标签ID
     */
    private Long id;
    
    /**
     * 标签名称
     */
    private String name;
    
    /**
     * 标签描述
     */
    private String description;
    
    /**
     * 标签颜色（十六进制颜色代码）
     */
    private String color;
    
    /**
     * 标签分类
     */
    private String category;
    
    /**
     * 标签快捷键
     */
    private String shortcut;
    
    /**
     * 是否激活
     */
    private boolean active;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 所属项目信息
     */
    private ProjectSummary project;
    
    /**
     * 所属数据集信息
     */
    private DatasetSummary dataset;
    
    /**
     * 标签图标URL
     */
    private String iconUrl;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 创建者信息
     */
    private UserSummary createdBy;
    
    /**
     * 标签排序优先级
     */
    private Integer priority;
    
    /**
     * 标签元数据（JSON格式）
     */
    private String metadata;
    
    /**
     * 从Label实体创建LabelResponse
     * 
     * @param label 标签实体
     */
    public LabelResponse(Label label) {
        this.id = label.getId();
        this.name = label.getName();
        this.description = label.getDescription();
        this.color = label.getColor();
        this.category = label.getCategory();
        this.shortcut = label.getShortcut();
        this.active = label.isActive();
        this.usageCount = label.getUsageCount();
        this.iconUrl = label.getIconUrl();
        this.createdAt = label.getCreatedAt();
        this.updatedAt = label.getUpdatedAt();
        this.priority = label.getPriority();
        this.metadata = label.getMetadata();
        
        // 设置项目信息
        if (label.getProject() != null) {
            this.project = new ProjectSummary(
                label.getProject().getId(),
                label.getProject().getName(),
                label.getProject().getDescription()
            );
        }
        
        // 设置数据集信息
        if (label.getDataset() != null) {
            this.dataset = new DatasetSummary(
                label.getDataset().getId(),
                label.getDataset().getName(),
                label.getDataset().getDescription()
            );
        }
        
        // 设置创建者信息
        if (label.getCreatedBy() != null) {
            this.createdBy = new UserSummary(
                label.getCreatedBy().getId(),
                label.getCreatedBy().getUsername(),
                label.getCreatedBy().getFullName()
            );
        }
    }
    
    /**
     * 项目摘要信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProjectSummary {
        private Long id;
        private String name;
        private String description;
    }
    
    /**
     * 数据集摘要信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatasetSummary {
        private Long id;
        private String name;
        private String description;
    }
}
