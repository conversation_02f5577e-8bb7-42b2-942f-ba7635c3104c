package com.example.biaozhu.payload.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.Pattern;

/**
 * 标签请求类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelRequest {
    
    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    @Size(max = 100, message = "标签名称不能超过100个字符")
    private String name;
    
    /**
     * 标签描述
     */
    @Size(max = 500, message = "标签描述不能超过500个字符")
    private String description;
    
    /**
     * 标签颜色（十六进制颜色代码）
     */
    @Pattern(regexp = "^#[0-9A-Fa-f]{6}$", message = "颜色代码格式不正确，应为#RRGGBB格式")
    private String color;
    
    /**
     * 标签分类
     */
    @Size(max = 50, message = "标签分类不能超过50个字符")
    private String category;
    
    /**
     * 标签快捷键
     */
    @Size(max = 20, message = "标签快捷键不能超过20个字符")
    private String shortcut;
    
    /**
     * 是否激活
     */
    private Boolean active = true;
    
    /**
     * 所属项目ID
     */
    private Long projectId;
    
    /**
     * 所属数据集ID
     */
    private Long datasetId;
    
    /**
     * 标签图标URL
     */
    @Size(max = 255, message = "图标URL不能超过255个字符")
    private String iconUrl;
    
    /**
     * 标签排序优先级
     */
    private Integer priority = 0;
    
    /**
     * 标签元数据（JSON格式）
     */
    private String metadata;
}
