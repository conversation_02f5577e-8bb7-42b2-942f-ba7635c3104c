package com.example.biaozhu.payload.request;

import javax.validation.constraints.Email;
import javax.validation.constraints.Size;

/**
 * 用户更新请求DTO
 * 用于用户信息更新，避免直接使用User实体
 */
public class UserUpdateRequest {

    /**
     * 邮箱
     */
    @Size(max = 50, message = "邮箱长度不能超过50个字符")
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 全名
     */
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String fullName;

    /**
     * 个人简介
     */
    @Size(max = 200, message = "个人简介不能超过200个字符")
    private String bio;

    /**
     * 电话号码
     */
    @Size(max = 20, message = "电话号码不能超过20个字符")
    private String phone;

    /**
     * 头像URL
     */
    @Size(max = 255, message = "头像URL不能超过255个字符")
    private String avatarUrl;

    /**
     * 职位
     */
    @Size(max = 50, message = "职位不能超过50个字符")
    private String position;

    /**
     * 新密码（可选）
     */
    @Size(min = 6, max = 40, message = "密码长度必须在6到40个字符之间")
    private String password;

    /**
     * 团队ID（可选）
     */
    private Long teamId;

    // 构造函数
    public UserUpdateRequest() {
    }

    // Getters and Setters
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    @Override
    public String toString() {
        return "UserUpdateRequest{" +
                "email='" + email + '\'' +
                ", fullName='" + fullName + '\'' +
                ", bio='" + bio + '\'' +
                ", phone='" + phone + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", position='" + position + '\'' +
                ", teamId=" + teamId +
                '}';
    }
}
